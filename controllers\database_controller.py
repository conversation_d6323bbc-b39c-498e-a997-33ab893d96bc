"""
Database controller for handling database operations.
"""
from flask import Blueprint, request, jsonify
import logging
import oracledb
import psycopg
from functools import wraps
from database_connection import (
    get_oracle_connection, 
    get_postgresql_connection,
    close_oracle_connection,
    close_postgresql_connection
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

database_bp = Blueprint('database', __name__)

def handle_db_error(f):
    """数据库错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"Database operation failed: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500
    return decorated_function

def get_connection(pool):
    """获取Oracle连接"""
    return get_oracle_connection()

def get_pg_connection(pool):
    """获取PostgreSQL连接"""
    return get_postgresql_connection()

# 全局连接池变量（用于兼容性）
pool = None
poolpg = None

@database_bp.route('/api/database_schemas', methods=['POST'])
@handle_db_error
def get_database_schemas():
    """获取指定数据库实例中的所有schema"""
    try:
        data = request.json
        database = data.get('database', 'pg')
        host = data.get('host', 'default')

        schemas = []

        if database.lower() == 'oracle':
            # 获取Oracle实例中的所有schema
            if host == 'default':
                # 使用默认Oracle连接
                conn = get_connection(pool)
                try:
                    with conn.cursor() as cursor:
                        cursor.execute("""
                            SELECT username FROM all_users
                            WHERE username NOT IN ('ANONYMOUS','APPQOSSYS','AUDSYS','CTXSYS','DBSFWUSER','DBSNMP','DIP','DVF','DVSYS','ENGINE','GGSYS','GSMADMIN_INTERNAL','GSMCATUSER','GSMROOTUSER','GSMUSER','HR','LBACSYS','MDDATA','MDSYS','OJVMSYS','OLAPSYS','ORACLE_OCM','ORDDATA','ORDPLUGINS','ORDSYS','OUTLN','REMOTE_SCHEDULER_AGENT','SI_INFORMTN_SCHEMA','SYS','SYS$UMF','SYSBACKUP','SYSDG','SYSKM','SYSRAC','SYSTEM','WMSYS','XDB','XS$NULL')
                            ORDER BY username
                        """)
                        rows = cursor.fetchall()
                        schemas = [row[0] for row in rows]
                finally:
                    close_oracle_connection(conn)
            else:
                # 使用指定主机的Oracle连接
                try:
                    # 使用默认的Oracle用户名和密码，但连接到指定主机
                    dsn = f"{host}:1521/orcl"  # 默认端口和服务名
                    with oracledb.connect(
                        user="datachange",  # 默认用户名
                        password="drgs2019",  # 默认密码
                        dsn=dsn
                    ) as conn:
                        with conn.cursor() as cursor:
                            cursor.execute("""
                                SELECT username FROM all_users
                                WHERE username NOT IN ('ANONYMOUS','APPQOSSYS','AUDSYS','CTXSYS','DBSFWUSER','DBSNMP','DIP','DVF','DVSYS','ENGINE','GGSYS','GSMADMIN_INTERNAL','GSMCATUSER','GSMROOTUSER','GSMUSER','HR','LBACSYS','MDDATA','MDSYS','OJVMSYS','OLAPSYS','ORACLE_OCM','ORDDATA','ORDPLUGINS','ORDSYS','OUTLN','REMOTE_SCHEDULER_AGENT','SI_INFORMTN_SCHEMA','SYS','SYS$UMF','SYSBACKUP','SYSDG','SYSKM','SYSRAC','SYSTEM','WMSYS','XDB','XS$NULL')
                                ORDER BY username
                            """)
                            rows = cursor.fetchall()
                            schemas = [row[0] for row in rows]
                except Exception as e:
                    return jsonify({'success': False, 'error': f'连接Oracle主机 {host} 失败: {str(e)}'}), 500
        else:
            # 获取PostgreSQL实例中的所有schema
            if host == 'default':
                # 使用默认PostgreSQL连接
                conn = get_pg_connection(poolpg)
                try:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1') ORDER BY schema_name")
                        rows = cursor.fetchall()
                        schemas = [row[0] for row in rows]
                finally:
                    close_postgresql_connection(conn)
            else:
                # 使用指定主机的PostgreSQL连接
                try:
                    with psycopg.connect(
                        host=host,
                        port=5432,  # 默认端口
                        dbname="databasetools",  # 默认数据库名
                        user="postgres",  # 默认用户名
                        password="P@ssw0rd"  # 默认密码
                    ) as conn:
                        with conn.cursor() as cursor:
                            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1') ORDER BY schema_name")
                            rows = cursor.fetchall()
                            schemas = [row[0] for row in rows]
                except Exception as e:
                    return jsonify({'success': False, 'error': f'连接PostgreSQL主机 {host} 失败: {str(e)}'}), 500

        return jsonify({'success': True, 'schemas': schemas})

    except Exception as e:
        logger.error(f"获取数据库schema列表失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@database_bp.route('/api/rules/execute_sql', methods=['POST'])
@handle_db_error
def execute_sql():
    """执行SQL语句"""
    try:
        data = request.json
        sql = data.get('sql')
        database = data.get('database', 'pg')  # 默认使用PostgreSQL
        host = data.get('host', 'default')  # 默认使用默认主机
        schema = data.get('schema', '')  # 可选的schema

        if not sql:
            return jsonify({'success': False, 'error': 'SQL语句不能为空'}), 400

        # 为了安全起见，禁止执行危险的SQL操作
        sql_upper = sql.strip().upper()
        dangerous_keywords = ['UPDATE', 'DELETE', 'INSERT', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE', 'GRANT', 'REVOKE']

        # 检查是否包含危险关键词
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                return jsonify({'success': False, 'error': f'出于安全考虑，不允许执行包含 {keyword} 的语句'}), 400

        # 根据选择的数据库和主机执行SQL
        if database.lower() == 'oracle':
            # 使用Oracle数据库
            if host == 'default':
                # 使用默认Oracle连接池
                conn = get_connection(pool)
                try:
                    with conn.cursor() as cursor:
                        # 如果指定了schema，切换到该schema
                        if schema:
                            cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {schema}")

                        # 为了提高查询速度，自动添加行数限制
                        limited_sql = sql.strip()
                        if limited_sql.endswith(';'):
                            limited_sql = limited_sql[:-1]  # 移除末尾分号

                        # 对于Oracle，使用子查询包装的方式添加ROWNUM限制
                        if 'SELECT' in limited_sql.upper() and 'ROWNUM' not in limited_sql.upper():
                            limited_sql = f"SELECT * FROM ({limited_sql}) WHERE ROWNUM <= 10"

                        cursor.execute(limited_sql)

                        # 获取查询结果
                        columns = [col[0] for col in cursor.description] if cursor.description else []
                        rows = cursor.fetchall()

                        schema_info = f" - {schema}" if schema else ""
                        return jsonify({
                            'success': True,
                            'database': f'Oracle (默认主机){schema_info}',
                            'host': 'default',
                            'schema': schema,
                            'affected_rows': len(rows),
                            'columns': columns,
                            'data': rows[:20]  # 限制返回前20行数据
                        })
                finally:
                    close_oracle_connection(conn)
            else:
                # 使用指定主机的Oracle连接
                try:
                    dsn = f"{host}:1521/orcl"
                    with oracledb.connect(
                        user="datachange",
                        password="drgs2019",
                        dsn=dsn
                    ) as conn:
                        with conn.cursor() as cursor:
                            # 如果指定了schema，切换到该schema
                            if schema:
                                cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {schema}")

                            # 为了提高查询速度，自动添加行数限制
                            limited_sql = sql.strip()
                            if limited_sql.endswith(';'):
                                limited_sql = limited_sql[:-1]  # 移除末尾分号

                            # 对于Oracle，使用子查询包装的方式添加ROWNUM限制
                            if 'SELECT' in limited_sql.upper() and 'ROWNUM' not in limited_sql.upper():
                                limited_sql = f"SELECT * FROM ({limited_sql}) WHERE ROWNUM <= 10"

                            cursor.execute(limited_sql)

                            # 获取查询结果
                            columns = [col[0] for col in cursor.description] if cursor.description else []
                            rows = cursor.fetchall()

                            schema_info = f" - {schema}" if schema else ""
                            return jsonify({
                                'success': True,
                                'database': f'Oracle ({host}){schema_info}',
                                'host': host,
                                'schema': schema,
                                'affected_rows': len(rows),
                                'columns': columns,
                                'data': rows[:100]  # 限制返回前100行数据
                            })
                except Exception as e:
                    return jsonify({'success': False, 'error': f'连接Oracle主机 {host} 失败: {str(e)}'}), 500
        else:
            # 使用PostgreSQL数据库
            if host == 'default':
                # 使用默认PostgreSQL连接池
                conn = get_pg_connection(poolpg)
                try:
                    with conn.cursor() as cursor:
                        # 如果指定了schema，设置search_path
                        if schema:
                            cursor.execute(f"SET search_path TO \"{schema}\", public")
                            # 验证search_path是否设置成功
                            cursor.execute("SHOW search_path")
                            current_path = cursor.fetchone()[0]
                            logger.info(f"PostgreSQL search_path已设置为: {current_path}")

                        # 为了提高查询速度，自动添加行数限制
                        limited_sql = sql.strip()
                        if not limited_sql.upper().endswith(';'):
                            limited_sql += ';'
                        # 对于PostgreSQL，添加LIMIT限制
                        if 'SELECT' in limited_sql.upper() and 'LIMIT' not in limited_sql.upper():
                            limited_sql = limited_sql.replace(';', ' LIMIT 10;')

                        cursor.execute(limited_sql)

                        # 获取查询结果
                        columns = [col[0] for col in cursor.description] if cursor.description else []
                        rows = cursor.fetchall()

                        schema_info = f" - {schema}" if schema else ""
                        return jsonify({
                            'success': True,
                            'database': f'PostgreSQL (默认主机){schema_info}',
                            'host': 'default',
                            'schema': schema,
                            'affected_rows': len(rows),
                            'columns': columns,
                            'data': rows[:100]  # 限制返回前100行数据
                        })
                finally:
                    close_postgresql_connection(conn)
            else:
                # 使用指定主机的PostgreSQL连接
                try:
                    with psycopg.connect(
                        host=host,
                        port=5432,
                        dbname="databasetools",
                        user="postgres",
                        password="P@ssw0rd"
                    ) as conn:
                        with conn.cursor() as cursor:
                            # 如果指定了schema，设置search_path
                            if schema:
                                cursor.execute(f"SET search_path TO \"{schema}\", public")
                                # 验证search_path是否设置成功
                                cursor.execute("SHOW search_path")
                                current_path = cursor.fetchone()[0]
                                logger.info(f"PostgreSQL search_path已设置为: {current_path}")

                            # 为了提高查询速度，自动添加行数限制
                            limited_sql = sql.strip()
                            if not limited_sql.upper().endswith(';'):
                                limited_sql += ';'
                            # 对于PostgreSQL，添加LIMIT限制
                            if 'SELECT' in limited_sql.upper() and 'LIMIT' not in limited_sql.upper():
                                limited_sql = limited_sql.replace(';', ' LIMIT 10;')

                            cursor.execute(limited_sql)

                            # 获取查询结果
                            columns = [col[0] for col in cursor.description] if cursor.description else []
                            rows = cursor.fetchall()

                            schema_info = f" - {schema}" if schema else ""
                            return jsonify({
                                'success': True,
                                'database': f'PostgreSQL ({host}){schema_info}',
                                'host': host,
                                'schema': schema,
                                'affected_rows': len(rows),
                                'columns': columns,
                                'data': rows[:100]  # 限制返回前100行数据
                            })
                except Exception as e:
                    return jsonify({'success': False, 'error': f'连接PostgreSQL主机 {host} 失败: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"执行SQL失败: {str(e)}")
        return jsonify({'success': False, 'error': f'SQL执行失败: {str(e)}'}), 500 

@database_bp.route('/api/database/config/save', methods=['POST'])
@handle_db_error
def save_database_config():
    """保存数据库连接配置"""
    try:
        data = request.json
        config_type = data.get('type', 'pg')  # pg 或 oracle
        host = data.get('host', '')
        port = data.get('port', '')
        username = data.get('username', '')
        password = data.get('password', '')
        database = data.get('database', '')
        # 导入配置管理模块
        from utils.config import config
        
        # 加载现有配置
        current_config = config.load_database_config()
        
        # 更新配置
        if config_type.lower() == 'oracle':
            current_config['oracle'] = {
                'host': host,
                'port': port,
                'username': username,
                'password': password,
                'dsn': database
            }
        else:
            current_config['postgresql'] = {
                'host': host,
                'port': port,
                'username': username,
                'password': password,
                'database': database
            }
        
        # 保存配置到文件
        if config.save_database_config(current_config):
            logger.info(f"保存数据库配置成功: {config_type}, {host}:{port}")
            return jsonify({
                'success': True,
                'message': '数据库配置已保存',
                'config': {
                    'type': config_type,
                    'host': host,
                    'port': port,
                    'username': username,
                    'password': password,
                    'database': database
                }
            })
        else:
            return jsonify({'success': False, 'error': '保存配置失败'}), 500
        
    except Exception as e:
        logger.error(f"保存数据库配置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@database_bp.route('/api/database/config/load', methods=['GET'])
@handle_db_error
def load_database_config():
    """加载数据库连接配置"""
    try:
        from utils.config import config
        
        # 加载配置
        db_config = config.load_database_config()
        
        return jsonify({
            'success': True,
            'config': db_config
        })
        
    except Exception as e:
        logger.error(f"加载数据库配置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500 

@database_bp.route('/api/database/test-connection', methods=['POST'])
@handle_db_error
def test_database_connection():
    """测试数据库连接"""
    try:
        data = request.json
        host = data.get('host')
        port = data.get('port', 5432)
        database = data.get('database')
        username = data.get('username')
        password = data.get('password')
        
        if not all([host, database, username, password]):
            return jsonify({'success': False, 'error': '缺少必要的连接参数'}), 400
        
        # 测试PostgreSQL连接
        try:
            conn_str = f"host={host} port={port} dbname={database} user={username} password={password}"
            with psycopg.connect(conn_str) as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    if result and result[0] == 1:
                        return jsonify({'success': True, 'message': '连接测试成功'})
                    else:
                        return jsonify({'success': False, 'error': '连接测试失败'})
        except Exception as e:
            return jsonify({'success': False, 'error': f'连接失败: {str(e)}'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@database_bp.route('/api/database/check-requirements', methods=['POST'])
@handle_db_error
def check_database_requirements():
    """检查数据库中的新规则需求"""
    try:
        data = request.json
        host = data.get('host')
        port = data.get('port', 5432)
        database = data.get('database')
        username = data.get('username')
        password = data.get('password')
        
        if not all([host, database, username, password]):
            return jsonify({'success': False, 'error': '缺少必要的连接参数'}), 400
        
        # 连接数据库并检查rule_requirement_input_info表
        try:
            conn_str = f"host={host} port={port} dbname={database} user={username} password={password}"
            with psycopg.connect(conn_str) as conn:
                with conn.cursor() as cursor:
                    # 检查表是否存在
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = 'rule_requirement_input_info'
                        )
                    """)
                    table_exists = cursor.fetchone()[0]
                    
                    if not table_exists:
                        return jsonify({'success': False, 'error': '表rule_requirement_input_info不存在'})
                    
                    # 查询execution_result为0的新需求
                    cursor.execute("""
                        SELECT rule_name, create_time, rule_intension
                        FROM rule_requirement_input_info 
                        WHERE execution_result = 0 and status='1'
                        ORDER BY create_time DESC
                    """)
                    
                    rows = cursor.fetchall()
                    new_requirements = []
                    
                    for row in rows:
                        new_requirements.append({
                            'rule_name': row[0],
                            'created_at': row[1].isoformat() if row[1] else None,
                            'description': row[2]
                        })
                    
                    return jsonify({
                        'success': True,
                        'newRequirements': new_requirements,
                        'count': len(new_requirements)
                    })
                    
        except Exception as e:
            return jsonify({'success': False, 'error': f'查询失败: {str(e)}'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500 

@database_bp.route('/api/rules/import', methods=['POST'])
@handle_db_error
def import_rules():
    """从指定数据库导入规则"""
    try:
        data = request.json
        connection_id = data.get('connection_id')
        connection_data = data.get('connection_data')
        
        # 使用新的导入服务
        from services.rule_import_service import RuleImportService
        import_service = RuleImportService()
        
        try:
            result = import_service.scan_importable_rules(
                connection_id=connection_id,
                connection_data=connection_data
            )
            return jsonify(result)
        except Exception as e:
            logger.error(f"扫描可导入规则失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})
            
    except Exception as e:
        logger.error(f"导入规则查询失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@database_bp.route('/api/rules/import/execute', methods=['POST'])
@handle_db_error
def execute_rule_import():
    """执行规则导入"""
    try:
        data = request.json
        selected_rules = data.get('selected_rules', [])
        overwrite = data.get('overwrite', False)
        connection_id = data.get('connection_id')
        connection_data = data.get('connection_data')
        
        if not selected_rules:
            return jsonify({'success': False, 'error': '没有选择要导入的规则'}), 400
        
        # 使用新的导入服务
        from services.rule_import_service import RuleImportService
        import_service = RuleImportService()
        
        try:
            result = import_service.import_rules(
                connection_id=connection_id,
                connection_data=connection_data,
                selected_rule_ids=selected_rules,
                overwrite=overwrite
            )
            return jsonify(result)
        except Exception as e:
            logger.error(f"执行规则导入失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})
        
    except Exception as e:
        logger.error(f"执行规则导入失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500 

@database_bp.route('/api/database/connections', methods=['GET'])
@handle_db_error
def get_database_connections():
    """获取所有保存的数据库连接配置"""
    try:
        from utils.config import config
        
        connections = config.load_database_connections()
        
        # 转换为列表格式，便于前端处理
        connections_list = []
        for connection_id, connection_data in connections.items():
            connections_list.append({
                'id': connection_id,
                **connection_data
            })
        
        return jsonify({
            'success': True,
            'connections': connections_list
        })
        
    except Exception as e:
        logger.error(f"获取数据库连接配置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@database_bp.route('/api/database/connections', methods=['POST'])
@handle_db_error
def save_database_connection():
    """保存数据库连接配置"""
    try:
        data = request.json
        from utils.config import config
        
        result = config.save_database_connection(data)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"保存数据库连接配置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@database_bp.route('/api/database/connections/<connection_id>', methods=['PUT'])
@handle_db_error
def update_database_connection(connection_id):
    """更新数据库连接配置"""
    try:
        data = request.json
        from utils.config import config
        
        result = config.update_database_connection(connection_id, data)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"更新数据库连接配置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@database_bp.route('/api/database/connections/<connection_id>', methods=['DELETE'])
@handle_db_error
def delete_database_connection(connection_id):
    """删除数据库连接配置"""
    try:
        from utils.config import config
        
        result = config.delete_database_connection(connection_id)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"删除数据库连接配置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@database_bp.route('/api/database/connections/<connection_id>', methods=['GET'])
@handle_db_error
def get_database_connection(connection_id):
    """获取指定的数据库连接配置"""
    try:
        from utils.config import config
        
        connection = config.get_database_connection(connection_id)
        
        if connection:
            return jsonify({
                'success': True,
                'connection': connection
            })
        else:
            return jsonify({'success': False, 'error': '连接配置不存在'}), 404
            
    except Exception as e:
        logger.error(f"获取数据库连接配置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500 