"""
Configuration management module for the rule management system.
"""
import os
import json
from typing import Dict, Any


class Config:
    """Application configuration class."""
    
    def __init__(self):
        self.BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.OUTPUT_DIR = os.path.join(self.BASE_DIR, 'output')
        self.TEMPLATES_DIR = os.path.join(self.BASE_DIR, 'templates')
        self.TEMPLATES_RULE_DIR = os.path.join(self.TEMPLATES_DIR, 'rule')
        self.CONFIG_DIR = os.path.join(self.BASE_DIR, 'config')
        self.DB_CONFIG_FILE = os.path.join(self.CONFIG_DIR, 'database_config.json')
        
        # Flask configuration
        self.FLASK_CONFIG = {
            'TEMPLATES_AUTO_RELOAD': True,
            'SEND_FILE_MAX_AGE_DEFAULT': 0,
            'DEBUG': os.getenv('DEBUG', 'True').lower() == 'true'
        }
        
        # Ensure required directories exist
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure all required directories exist."""
        directories = [
            self.OUTPUT_DIR,
            self.TEMPLATES_DIR,
            self.TEMPLATES_RULE_DIR,
            self.CONFIG_DIR
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    def get_flask_config(self) -> Dict[str, Any]:
        """Get Flask configuration dictionary."""
        return self.FLASK_CONFIG.copy()
    
    def save_database_config(self, config_data: Dict[str, Any]) -> bool:
        """保存数据库配置到文件"""
        try:
            # 确保配置目录存在
            self._ensure_directories()
            
            # 保存配置到JSON文件
            with open(self.DB_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存数据库配置失败: {str(e)}")
            return False
    
    def load_database_config(self) -> Dict[str, Any]:
        """从文件加载数据库配置"""
        try:
            if not os.path.exists(self.DB_CONFIG_FILE):
                # 如果配置文件不存在，返回默认配置
                return {
                    'oracle': {
                        'host': '127.0.0.1',
                        'port': '1521',
                        'username': 'datachange',
                        'password': 'drgs2019',
                        'dsn': 'orcl'
                    },
                    'postgresql': {
                        'host': '*************',
                        'port': '5432',
                        'username': 'postgres',
                        'password': 'P@ssw0rd',
                        'database': 'databasetools'
                    }
                }
            
            with open(self.DB_CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            return config
        except Exception as e:
            print(f"加载数据库配置失败: {str(e)}")
            # 返回默认配置
            return {
                'oracle': {
                    'host': '127.0.0.1',
                    'port': '1521',
                    'username': 'datachange',
                    'password': 'drgs2019',
                    'dsn': 'orcl'
                },
                'postgresql': {
                    'host': '*************',
                    'port': '5432',
                    'username': 'postgres',
                    'password': 'P@ssw0rd',
                    'database': 'databasetools'
                }
            }


# Global configuration instance
config = Config()