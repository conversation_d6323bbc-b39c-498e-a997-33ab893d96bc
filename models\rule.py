"""
Data models for rule management.
"""
from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime


@dataclass
class RuleInfo:
    """Basic rule information model."""
    name: str
    created_at: float
    file_size: Optional[int] = None
    status: str = "active"
    description: Optional[str] = None
    category: Optional[str] = None
    rule_type: Optional[str] = None
    policy_basis: Optional[str] = None


@dataclass
class Rule:
    """Complete rule model."""
    name: str
    content: str
    created_at: Optional[float] = None
    file_size: Optional[int] = None
    status: str = "active"
    description: Optional[str] = None
    category: Optional[str] = None
    rule_type: Optional[str] = None
    policy_basis: Optional[str] = None
    template_id: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    database_type: Optional[str] = None
    created_by: Optional[str] = None
    updated_at: Optional[float] = None
    # New attributes for intelligent template selection
    patient_type: Optional[str] = None  # inpatient, outpatient, general
    match_method: Optional[str] = None  # name, code
    applicable_scope: Optional[str] = None  # 门诊/住院/通用


@dataclass
class RuleData:
    """Rule data for creation/update operations."""
    name: str
    content: str
    description: Optional[str] = None
    category: Optional[str] = None
    rule_type: Optional[str] = None
    policy_basis: Optional[str] = None
    template_id: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    database_type: Optional[str] = None
    created_by: Optional[str] = None
    # New attributes for intelligent template selection
    patient_type: Optional[str] = None  # inpatient, outpatient, general
    match_method: Optional[str] = None  # name, code
    applicable_scope: Optional[str] = None  # 门诊/住院/通用
    
    def validate(self) -> bool:
        """Validate rule data."""
        if not self.name or not self.name.strip():
            return False
        if not self.content or not self.content.strip():
            return False
        return True


@dataclass
class TemplateParameter:
    """Template parameter model."""
    name: str
    description: Optional[str] = None
    parameter_type: str = "text"  # text, number, list, enum
    required: bool = True
    default_value: Optional[str] = None


@dataclass
class TemplateInfo:
    """Template information model."""
    id: str
    description: str
    sql: str
    category: Optional[str] = None
    parameters: Optional[list] = None
    database_type: Optional[str] = None
    patient_type: Optional[str] = None


@dataclass
class GenerationRequest:
    """SQL generation request model."""
    template_id: str
    parameters: Dict[str, Any]
    database_type: str = "postgresql"