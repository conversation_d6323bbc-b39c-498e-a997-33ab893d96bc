{"description": "通过智能向导创建的测试规则", "category": "intelligent", "rule_type": "超频次", "policy_basis": "医保政策文件XYZ", "template_id": "rule_pg_name_inpatient/住院期间超天数 - 旧版本多算了0的那天", "parameters": {"医保名称1": "测试医保项目1,测试医保项目2", "违规数量": "3"}, "database_type": "postgresql", "created_by": null, "status": "deleted", "created_at": "2025-08-03T09:35:57.745073", "updated_at": "2025-08-03T10:04:36.236495", "patient_type": "inpatient", "match_method": "name", "applicable_scope": null}