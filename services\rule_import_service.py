"""
Rule import service for importing rules from external databases.
"""
import psycopg
import logging
import uuid
from typing import List, Dict, Any, Optional
from models.rule import RuleData
from services.rule_service import RuleManagementService
from utils.error_handler import BusinessError, SystemError
from utils.config import config

logger = logging.getLogger(__name__)

class RuleImportService:
    """Service for importing rules from external databases."""
    
    def __init__(self):
        self.rule_service = RuleManagementService()
    
    def get_connection_info(self, connection_id: str = None, connection_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        获取数据库连接信息
        
        Args:
            connection_id: 保存的连接配置ID
            connection_data: 直接提供的连接数据
            
        Returns:
            连接信息字典
        """
        if connection_id:
            # 从保存的连接配置中获取
            connection = config.get_database_connection(connection_id)
            if not connection:
                raise BusinessError(f"连接配置 {connection_id} 不存在")
            return connection
        elif connection_data:
            # 使用直接提供的连接数据
            return connection_data
        else:
            raise BusinessError("必须提供连接配置ID或连接数据")
    
    def scan_importable_rules(self, connection_id: str = None, connection_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        扫描可导入的规则
        
        Args:
            connection_id: 保存的连接配置ID
            connection_data: 直接提供的连接数据
            
        Returns:
            包含可导入规则列表的字典
        """
        try:
            # 获取连接信息
            conn_info = self.get_connection_info(connection_id, connection_data)
            
            host = conn_info.get('host', 'localhost')
            port = conn_info.get('port', 5432)
            database = conn_info.get('database', 'databasetools')
            username = conn_info.get('username', 'postgres')
            password = conn_info.get('password', 'P@ssw0rd')
            
            conn_str = f"host={host} port={port} dbname={database} user={username} password={password}"
            with psycopg.connect(conn_str) as conn:
                with conn.cursor() as cursor:
                    # 检查kj_rule表是否存在
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = 'kj_rule'
                        )
                    """)
                    table_exists = cursor.fetchone()[0]
                    
                    if not table_exists:
                        return {
                            'success': True,
                            'rules': [],
                            'count': 0,
                            'message': '目标数据库中不存在kj_rule表，无法导入规则',
                            'connection_info': {
                                'name': conn_info.get('name', ''),
                                'host': host,
                                'port': port,
                                'database': database
                            }
                        }
                    
                    # 查询可导入的规则（sql_name为空的规则）
                    cursor.execute("""
                        SELECT id, rule_name, rule_intension, policy_basis, create_time
                        FROM public.kj_rule 
                        WHERE sql_name IS NULL OR sql_name = '' OR sql_name = 'NULL'
                        ORDER BY create_time DESC
                    """)
                    
                    rows = cursor.fetchall()
                    importable_rules = []
                    
                    for row in rows:
                        # 安全地处理查询结果，防止索引越界
                        if len(row) >= 4:
                            rule_data = {
                                'id': row[0],
                                'rule_name': row[1] if row[1] else '未命名规则',
                                'rule_intension': row[2] if row[2] else '',
                                'policy_basis': row[3] if len(row) > 3 and row[3] else '',
                                'create_time': row[4].isoformat() if len(row) > 4 and row[4] else None
                            }
                            importable_rules.append(rule_data)
                        else:
                            logger.warning(f"跳过字段不足的规则行: {row}")
                    
                    return {
                        'success': True,
                        'rules': importable_rules,
                        'count': len(importable_rules),
                        'connection_info': {
                            'name': conn_info.get('name', ''),
                            'host': host,
                            'port': port,
                            'database': database
                        }
                    }
                    
        except Exception as e:
            logger.error(f"扫描可导入规则失败: {str(e)}")
            raise SystemError(f"扫描可导入规则失败: {str(e)}")
    
    def import_rules(self, connection_id: str = None, connection_data: Dict[str, Any] = None,
                    selected_rule_ids: List[int] = None, overwrite: bool = False) -> Dict[str, Any]:
        """
        导入选定的规则
        
        Args:
            connection_id: 保存的连接配置ID
            connection_data: 直接提供的连接数据
            selected_rule_ids: 选定的规则ID列表
            overwrite: 是否覆盖现有规则
            
        Returns:
            导入结果字典
        """
        try:
            # 获取连接信息
            conn_info = self.get_connection_info(connection_id, connection_data)
            
            host = conn_info.get('host', 'localhost')
            port = conn_info.get('port', 5432)
            database = conn_info.get('database', 'databasetools')
            username = conn_info.get('username', 'postgres')
            password = conn_info.get('password', 'P@ssw0rd')
            
            imported_count = 0
            failed_rules = []
            
            # 连接数据库获取规则详情
            conn_str = f"host={host} port={port} dbname={database} user={username} password={password}"
            with psycopg.connect(conn_str) as conn:
                with conn.cursor() as cursor:
                    for rule_id in selected_rule_ids:
                        try:
                            # 获取规则详情
                            cursor.execute("""
                                SELECT id, rule_name, rule_intension,policy_basis, create_time
                                FROM public.kj_rule 
                                WHERE id = %s
                            """, (str(rule_id),))
                            
                            row = cursor.fetchone()
                            if not row:
                                failed_rules.append(f"规则ID {rule_id}: 规则不存在")
                                continue
                            
                            # 安全地处理查询结果
                            if len(row) >= 4:
                                # 直接使用原始数据库中的id，转换为字符串
                                rule_id_str = str(row[0])  # 原始数据库ID
                                
                                rule_name = row[1] if row[1] else f'导入规则_{rule_id}'
                                rule_intension = row[2] if row[2] else ''
                                policy_basis = row[3] if len(row) > 3 and row[3] else ''
                                
                                # 分析规则名称，智能判断规则类型
                                rule_type = self._analyze_rule_type(rule_name, rule_intension)
                                
                                # 创建RuleData对象，支持导入数据结构
                                rule_data = RuleData(
                                    id=rule_id_str,  # 使用原始数据库ID
                                    name=rule_name,
                                    content=rule_intension,
                                    description=policy_basis,
                                    category='导入规则',
                                    rule_type=rule_type,
                                    policy_basis=policy_basis,  # 保存政策依据
                                    database_type='postgresql',
                                    patient_type='general',
                                    match_method='name'
                                )
                                
                                # 保存规则
                                self.rule_service.save_rule(rule_data, allow_overwrite=overwrite)
                                imported_count += 1
                                
                                logger.info(f"成功导入规则: {rule_name}")
                                
                            else:
                                failed_rules.append(f"规则ID {rule_id}: 数据格式错误")
                                
                        except Exception as e:
                            error_msg = f"规则ID {rule_id}: {str(e)}"
                            failed_rules.append(error_msg)
                            logger.error(error_msg)
            
            # 返回导入结果
            result = {
                'success': True,
                'imported_count': imported_count,
                'failed_count': len(failed_rules),
                'failed_rules': failed_rules,
                'connection_info': {
                    'name': conn_info.get('name', ''),
                    'host': host,
                    'port': port,
                    'database': database
                }
            }
            
            if failed_rules:
                result['message'] = f'成功导入 {imported_count} 个规则，{len(failed_rules)} 个失败'
            else:
                result['message'] = f'成功导入 {imported_count} 个规则'
            
            return result
            
        except Exception as e:
            logger.error(f"导入规则失败: {str(e)}")
            raise SystemError(f"导入规则失败: {str(e)}")
    
    def _analyze_rule_type(self, rule_name: str, rule_content: str) -> str:
        """
        分析规则名称和内容，智能判断规则类型
        
        Args:
            rule_name: 规则名称
            rule_content: 规则内容
            
        Returns:
            规则类型
        """
        # 基于规则名称的关键词分析
        name_lower = rule_name.lower()
        content_lower = rule_content.lower()
        
        # 定义规则类型关键词映射
        type_keywords = {
            '超频次': ['超频次', '超次数', '频次'],
            '重复收费': ['重复收费', '重复','同时', '重复计费'],
            '限性别': ['限性别', '性别限制'],
            '限年龄': ['限年龄', '年龄限制'],
            '病例提取': ['病例提取', '提取病例'],
            '多项合并': ['多项合并', '合并项目'],
            '组套收费': ['组套收费', '组套'],
            '禁忌药物': ['禁忌药物', '禁忌'],
            '诊断项目不匹配': ['诊断项目不匹配', '诊断不匹配'],
            '超用药金额': ['超用药金额', '超金额'],
            '超合理用药疗程': ['超合理用药疗程', '超疗程'],
            '限医保等级': ['限医保等级', '医保等级']
        }
        
        # 检查规则名称中的关键词
        for rule_type, keywords in type_keywords.items():
            for keyword in keywords:
                if keyword in name_lower:
                    return rule_type
        
        # 检查规则内容中的关键词
        for rule_type, keywords in type_keywords.items():
            for keyword in keywords:
                if keyword in content_lower:
                    return rule_type
        
        # 默认返回病例提取
        return '病例提取'
    
    def validate_import_data(self, rule_data: Dict[str, Any]) -> List[str]:
        """
        验证导入的规则数据
        
        Args:
            rule_data: 规则数据
            
        Returns:
            验证错误列表
        """
        errors = []
        
        if not rule_data.get('rule_name'):
            errors.append("规则名称不能为空")
        
        if not rule_data.get('rule_content'):
            errors.append("规则内容不能为空")
        
        # 检查规则名称长度
        rule_name = rule_data.get('rule_name', '')
        if len(rule_name) > 100:
            errors.append("规则名称过长（超过100字符）")
        
        # 检查规则内容长度
        rule_content = rule_data.get('rule_content', '')
        if len(rule_content) > 10000:
            errors.append("规则内容过长（超过10000字符）")
        
        return errors 